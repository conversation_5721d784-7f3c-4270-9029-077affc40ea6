import { Router<PERSON>rovider } from '@tanstack/react-router';
import { router } from './routes';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { CertificateProvider } from './contexts/CertificateContext';
import { useEffect, useMemo } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Create a client with optimized global defaults to prevent excessive re-renders
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Prevent refetching on window focus globally - key optimization for tab switching
      refetchOnWindowFocus: false,
      // Only refetch if data is stale, not on every mount
      refetchOnMount: false,
      // Don't refetch on network reconnect unless explicitly needed
      refetchOnReconnect: false,
      // Set reasonable stale time to prevent unnecessary refetches
      staleTime: 5 * 60 * 1000, // 5 minutes
      // Keep data in cache longer to improve performance
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      // Reduce retry attempts for better performance
      retry: 1,
      // Shorter delay between retries
      retryDelay: 1000,
    },
    mutations: {
      // Reduce retry attempts for mutations
      retry: 1,
      retryDelay: 1000,
    },
  },
});

function AppWithAuth() {
  const { user, session, loading } = useAuth();

  // Memoize router context to prevent unnecessary updates
  const routerContext = useMemo(() => ({
    user,
    session,
    loading,
  }), [user?.id, session?.access_token, loading]); // More specific dependencies

  useEffect(() => {
    // Only update the router context when authentication succeeds or when the component mounts
    // This prevents router updates on failed login attempts
    if (!loading) { // Only update when loading is complete
      console.log('Updating router context with:', routerContext);
      router.update({
        context: routerContext,
      });
    }
  }, [routerContext, loading]);

    return (
      <>
        {loading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="text-center bg-white p-6 rounded-lg">
              <div className="w-16 h-16 border-t-4 border-green-500 border-solid rounded-full animate-spin mx-auto"></div>
              <p className="mt-4 text-gray-600">Credentials werden geprüft...</p>
            </div>
          </div>
        )}
        <RouterProvider router={router} />
      </>
    );
    
}

function App() {
  return (
    <AuthProvider>
      <QueryClientProvider client={queryClient}>
        <CertificateProvider>
          <AppWithAuth />
        </CertificateProvider>
        {/* React Query DevTools - only in development */}
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </AuthProvider>
  );
}

export default App;
